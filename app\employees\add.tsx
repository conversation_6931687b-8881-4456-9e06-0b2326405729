import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Image as RNImage,
  Alert,
  Platform,
  ActivityIndicator
} from 'react-native';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { colors } from '@/constants/colors';
import { User, Mail, Phone, Calendar, Image as ImageIcon, X, ChevronLeft, Save,MapPin } from 'lucide-react-native';
import { useThemeColors } from '@/hooks/useThemeColors';
import * as ImagePicker from 'expo-image-picker';
import DateTimePicker from '@react-native-community/datetimepicker';
import { format } from 'date-fns';
import { useTranslation } from '@/hooks/useTranslation';
import { uploadImage } from '@/services/storage-service';
import { useFarmStore } from '@/store/farm-store';
import { useAuthStore } from '@/store/auth-store';
import MultiSelectDropdown from '@/components/MultiSelectDropdown';
import { useToast } from '@/contexts/ToastContext';
import LoadingOverlay from '@/components/LoadingOverlay';
import { registerUserWithEmailVerification } from '@/services/user-registration-service';

export default function AddEmployeeScreen() {
  const { t } = useTranslation();
  const router = useRouter();
  const { user } = useAuthStore();
  const params = useLocalSearchParams<{ farmId: string }>();
  const farmId = params.farmId;
  const { showToast } = useToast();
  const themedColors = useThemeColors();

  // Convert farmId to string if it's an object (this can happen with URL params)
  let normalizedFarmId = typeof farmId === 'object' ? JSON.stringify(farmId) : farmId;

  // Additional farmId normalization
  if (normalizedFarmId) {
    // Remove quotes if it's a stringified value
    if (normalizedFarmId.startsWith('"') && normalizedFarmId.endsWith('"')) {
      normalizedFarmId = normalizedFarmId.slice(1, -1);
    }
  }

  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [cnic, setCnic] = useState('');
  const [age, setAge] = useState('');
  const [gender, setGender] = useState('male'); // 'male' or 'female'
  const [role, setRole] = useState('caretaker'); // 'admin' or 'caretaker'
  const [joiningDate, setJoiningDate] = useState(new Date());
  const [status, setStatus] = useState('active'); // 'active' or 'inactive'
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [photo, setPhoto] = useState('');
  const [selectedFarmIds, setSelectedFarmIds] = useState<string[]>([]);
  const { farms, fetchFarms } = useFarmStore();

  useEffect(() => {
    if (user?.id) {
      fetchFarms(user.id);
    }
  }, [user]);

  // Validation errors
  const [validationErrors, setValidationErrors] = useState<{
    name?: string;
    email?: string;
    phone?: string;
    cnic?: string;
    age?: string;
    photo?: string;
    farms?: string;
  }>({});

  // Function to pick an image from the gallery
  const pickImage = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (status !== 'granted') {
        Alert.alert('Permission Denied', 'Permission to access media library is required');
        return;
      }
      // Using 'images' directly as MediaTypeOptions is deprecated
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.5,
      });

      if (!result.canceled) {
        const selectedAsset = result.assets[0];
        
        setPhoto(selectedAsset.uri);

        // Clear photo validation error if it exists
        if (validationErrors.photo) {
          setValidationErrors(prev => ({ ...prev, photo: undefined }));
        }
      }
    } catch (error) {
      Alert.alert(
        t('common.error'),
        t('validation.photoSelectionError')
      );
    }
  };

  // Check if email is already in use
  // const checkEmailExists = async (email: string): Promise<boolean> => {
  //   try {
  //     // Check in users collection
  //     const usersQuery = query(
  //       collection(firestore, 'users'),
  //       where('email', '==', email)
  //     );

  //     const usersSnapshot = await getDocs(usersQuery);

  //     return !usersSnapshot.empty;
  //   } catch (error) {
  //     return false;
  //   }
  // };

  // Validate form fields
  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!name.trim()) {
      errors.name = t('validation.required', { field: t('farms.staffSection.name') });
    }

    if (!email.trim()) {
      errors.email = t('validation.required', { field: t('auth.email') });
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      errors.email = t('validation.invalidEmail');
    }

    if (!phone.trim()) {
      errors.phone = t('validation.required', { field: t('auth.phone') });
    }

    if (!cnic.trim()) {
      errors.cnic = t('validation.required', { field: 'CNIC' });
    }

    if (!age.trim()) {
      errors.age = t('validation.required', { field: t('animals.age') });
    } else if (isNaN(Number(age)) || Number(age) <= 0) {
      errors.age = t('validation.invalidNumber', { field: t('animals.age') });
    }

    // Remove photo validation
    // if (!photo) {
    //   errors.photo = t('validation.required', { field: t('common.photo') });
    // }

    if (selectedFarmIds.length === 0) {
      errors.farms = t('validation.required', { field: t('farms.title') });
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      setIsLoading(true);

      // Upload photo if available
      let photoUrl = '';
      if (photo) {
        try {
          const timestamp = Date.now();
          const filename = `employees/${timestamp}_${Math.random().toString(36).substring(7)}.jpg`;

          photoUrl = await uploadImage(photo, filename);
        } catch (uploadError: any) {
          showToast({
            type: 'error',
            title: t('common.error'),
            message: uploadError.message || t('validation.photoUploadError')
          });
          setIsLoading(false);
          return;
        }
      }

      // Get farm names for email
      const farmNames = farms
        .filter(farm => selectedFarmIds.includes(farm.id))
        .map(farm => farm.name);

      // Use new registration flow with email verification
      const registrationResult = await registerUserWithEmailVerification(
        {
          email,
          name,
          role: role as 'admin' | 'caretaker',
          farmIds: selectedFarmIds,
          farmNames,
          phone_number: phone,
          cnic,
          age: parseInt(age) || 0,
          gender,
          photo: photoUrl
        },
        user?.id || ''
      );

      if (registrationResult.success) {
        // Show success message with instructions for password reset approach
        Alert.alert(
          t('farms.staffSection.addSuccess'),
          `User registered successfully!\n\nEmail: ${email}\nTemporary Password: ${registrationResult.tempPassword}\n\nIMPORTANT: A password reset email has been sent to ${email}.\n\nPlease tell the user to:\n1. Check their email for "Reset your password" message\n2. Click the link in the email\n3. Set their new password in the browser\n4. Then login to the app with their email and new password\n\nThe temporary password above is for reference only.`,
          [
            {
              text: 'Show Instructions',
              onPress: () => {
                Alert.alert(
                  'User Instructions',
                  `Please tell ${name} to:\n\n1. Check email: ${email}\n2. Look for "Reset your password" email from Firebase\n3. Click the link in the email\n4. Create a new password in the browser\n5. Download the Animal Health Tracker app\n6. Login with:\n   Email: ${email}\n   Password: [their new password]\n\nThey have 24 hours to complete this process.`
                );
              }
            },
            {
              text: 'OK',
              onPress: () => router.back(),
              style: 'default'
            }
          ]
        );
      } else {
        Alert.alert(t('common.error'), registrationResult.message);
      }
    } catch (error: any) {
      Alert.alert(t('common.error'), error.message || t('farms.staffSection.addError'));
    } finally {
      setIsLoading(false);
    }
  };



  const styles = getStyles(themedColors);

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <Stack.Screen
        options={{
          title: t('farms.staffSection.addEmployee'),
          headerLeft: () => (
            <TouchableOpacity onPress={() => router.back()}>
              <ChevronLeft size={24} color={themedColors.text} />
            </TouchableOpacity>
          ),
        }}
      />

      {isLoading && <LoadingOverlay />}

      <ScrollView style={styles.scrollView}>
        <View style={styles.formContainer}>
          {/* Photo Section */}
          <View style={styles.photoContainer}>
            {photo ? (
              <RNImage source={{ uri: photo }} style={styles.photo} />
            ) : (
              <TouchableOpacity style={styles.photoPlaceholder} onPress={pickImage}>
                <ImageIcon size={40} color={themedColors.textSecondary} />
              </TouchableOpacity>
            )}
            <TouchableOpacity onPress={pickImage}>
              <Text style={styles.photoText}>
                {photo ? t('farms.staffSection.changePhoto') : t('farms.staffSection.addPhoto')}
              </Text>
            </TouchableOpacity>
            {validationErrors.photo && (
              <Text style={styles.errorText}>{validationErrors.photo}</Text>
            )}
          </View>

          {/* Name */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>{t('farms.staffSection.name')}*</Text>
            <View style={[styles.inputContainer, validationErrors.name && styles.inputError]}>
              <User size={20} color={themedColors.textSecondary} style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                placeholder={t('farms.staffSection.namePlaceholder')}
                value={name}
                onChangeText={setName}
              />
            </View>
            {validationErrors.name && (
              <Text style={styles.errorText}>{validationErrors.name}</Text>
            )}
          </View>

          {/* Email */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>{t('auth.email')}*</Text>
            <View style={[styles.inputContainer, validationErrors.email && styles.inputError]}>
              <Mail size={20} color={themedColors.textSecondary} style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                placeholder="<EMAIL>"
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
              />
            </View>
            {validationErrors.email && (
              <Text style={styles.errorText}>{validationErrors.email}</Text>
            )}
          </View>

          {/* Phone */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>{t('auth.phone')}*</Text>
            <View style={[styles.inputContainer, validationErrors.phone && styles.inputError]}>
              <Phone size={20} color={themedColors.textSecondary} style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                placeholder="+92 300 1234567"
                value={phone}
                onChangeText={setPhone}
                keyboardType="phone-pad"
              />
            </View>
            {validationErrors.phone && (
              <Text style={styles.errorText}>{validationErrors.phone}</Text>
            )}
          </View>

          {/* CNIC */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>{t('farms.staffSection.cnic')}*</Text>
            <View style={[styles.inputContainer, validationErrors.cnic && styles.inputError]}>
              <TextInput
                style={styles.input}
                placeholder="35201-1234567-8"
                value={cnic}
                onChangeText={setCnic}
                keyboardType="number-pad"
              />
            </View>
            {validationErrors.cnic && (
              <Text style={styles.errorText}>{validationErrors.cnic}</Text>
            )}
          </View>

          {/* Age */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>{t('animals.age')}*</Text>
            <View style={[styles.inputContainer, validationErrors.age && styles.inputError]}>
              <TextInput
                style={styles.input}
                placeholder="25"
                value={age}
                onChangeText={setAge}
                keyboardType="number-pad"
              />
            </View>
            {validationErrors.age && (
              <Text style={styles.errorText}>{validationErrors.age}</Text>
            )}
          </View>

          {/* Gender */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>{t('farms.staffSection.gender')}</Text>
            <View style={styles.radioGroup}>
              <TouchableOpacity
                style={[styles.radioButton, gender === 'male' && styles.radioButtonSelected]}
                onPress={() => setGender('male')}
              >
                <Text style={[styles.radioText, gender === 'male' && styles.radioTextSelected]}>
                  {t('common.male')}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.radioButton, gender === 'female' && styles.radioButtonSelected]}
                onPress={() => setGender('female')}
              >
                <Text style={[styles.radioText, gender === 'female' && styles.radioTextSelected]}>
                  {t('common.female')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Role */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>{t('farms.staffSection.role')}</Text>
            <View style={styles.radioGroup}>
              <TouchableOpacity
                style={[styles.radioButton, role === 'admin' && styles.radioButtonSelected]}
                onPress={() => setRole('admin')}
              >
                <Text style={[styles.radioText, role === 'admin' && styles.radioTextSelected]}>
                  {t('farms.staffSection.roles.admin')}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.radioButton, role === 'caretaker' && styles.radioButtonSelected]}
                onPress={() => setRole('caretaker')}
              >
                <Text style={[styles.radioText, role === 'caretaker' && styles.radioTextSelected]}>
                  {t('farms.staffSection.roles.caretaker')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Joining Date */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>{t('farms.staffSection.joiningDate')}</Text>
            <TouchableOpacity
              style={styles.inputContainer}
              onPress={() => setShowDatePicker(true)}
            >
              <Calendar size={20} color={themedColors.textSecondary} style={styles.inputIcon} />
              <Text style={styles.dateText}>
                {format(joiningDate, 'MMMM d, yyyy')}
              </Text>
            </TouchableOpacity>
            {showDatePicker && (
              <DateTimePicker
                value={joiningDate}
                mode="date"
                display="default"
                onChange={(_event, selectedDate) => {
                  setShowDatePicker(Platform.OS === 'ios');
                  if (selectedDate) {
                    setJoiningDate(selectedDate);
                  }
                }}
              />
            )}
          </View>

          {/* Status */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>{t('farms.staffSection.status')}</Text>
            <View style={styles.radioGroup}>
              <TouchableOpacity
                style={[styles.radioButton, status === 'active' && styles.radioButtonSelected]}
                onPress={() => setStatus('active')}
              >
                <Text style={[styles.radioText, status === 'active' && styles.radioTextSelected]}>
                  {t('common.active')}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.radioButton, status === 'inactive' && styles.radioButtonSelected]}
                onPress={() => setStatus('inactive')}
              >
                <Text style={[styles.radioText, status === 'inactive' && styles.radioTextSelected]}>
                  {t('common.inactive')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Farm Selection */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>{t('farms.selectFarms')}*</Text>
            <MultiSelectDropdown
              items={farms.map(farm => ({
                id: farm.id,
                label: farm.name,
                description: farm.location,
                icon: <MapPin size={20} color={colors.primary} />
              }))}
              selectedIds={selectedFarmIds}
              onSelectionChange={setSelectedFarmIds}
              placeholder={t('farms.selectFarmsPlaceholder')}
              modalTitle={t('farms.selectFarms')}
              searchPlaceholder={t('farms.searchFarms')}
            />
            {validationErrors.farms && (
              <Text style={styles.errorText}>{validationErrors.farms}</Text>
            )}
          </View>

          {/* Submit Button */}
          <TouchableOpacity
            style={[styles.saveButton, isLoading && styles.saveButtonDisabled]}
            onPress={handleSubmit}
            disabled={isLoading}
          >
            <Save size={20} color="#fff" />
            <Text style={styles.saveButtonText}>{t('common.save')}</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const getStyles = (themedColors: ReturnType<typeof useThemeColors>) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: themedColors.background,
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  formContainer: {
    marginBottom: 20,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: themedColors.text,
    marginBottom: 8,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: themedColors.border,
    borderRadius: 8,
    paddingHorizontal: 12,
    backgroundColor: themedColors.card, // Changed from inputBackground to card
  },
  inputIcon: {
    marginRight: 8,
  },
  input: {
    flex: 1,
    height: 48,
    color: themedColors.text,
    fontSize: 16,
  },
  photoContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  photo: {
    width: 120,
    height: 120,
    borderRadius: 60,
  },
  photoPlaceholder: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: themedColors.card, // Changed from inputBackground to card
    borderWidth: 1,
    borderColor: themedColors.border,
    alignItems: 'center',
    justifyContent: 'center',
  },
  photoText: {
    marginTop: 8,
    color: themedColors.textSecondary,
    fontSize: 14,
  },
  radioGroup: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  radioButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: themedColors.border,
    backgroundColor: themedColors.card, // Changed from inputBackground to card
    flex: 1,
    marginHorizontal: 4,
  },
  radioButtonSelected: {
    backgroundColor: themedColors.primary,
    borderColor: themedColors.primary,
  },
  radioText: {
    color: themedColors.text,
    marginLeft: 8,
    fontSize: 14,
  },
  radioTextSelected: {
    color: 'white',
  },
  dateText: {
    flex: 1,
    height: 48,
    color: themedColors.text,
    fontSize: 16,
    textAlignVertical: 'center',
  },
  submitButton: {
    backgroundColor: themedColors.primary,
    borderRadius: 8,
    paddingVertical: 14,
    alignItems: 'center',
    marginTop: 16,
  },
  submitButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  errorText: {
    color: themedColors.error,
    fontSize: 14,
    marginTop: 4,
  },
  inputError: {
    borderColor: themedColors.error,
  },
  // Add these styles for the save button
  saveButton: {
    backgroundColor: themedColors.primary,
    borderRadius: 8,
    paddingVertical: 14,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 16,
  },
  saveButtonDisabled: {
    backgroundColor: themedColors.border,
    opacity: 0.7,
  },
  saveButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
});
